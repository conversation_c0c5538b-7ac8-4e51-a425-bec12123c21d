#!/usr/bin/env python3
"""
简化版补充推理脚本
基于现有的ltxv_inference.py，专门处理gpu_7中推理失败的视频文件
"""

import os
import sys
import cv2
import torch
import tempfile
import logging
import subprocess
from pathlib import Path
from typing import List, Dict
from datetime import datetime
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('supplement_inference.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SimpleSupplementInference:
    """简化版补充推理器"""
    
    def __init__(self):
        """初始化"""
        self.missing_videos_file = Path("missing_videos.txt")
        self.test_val_dir = Path("/data1/wzy/LTXV_ALL/test_val")
        self.all_dir = Path("/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/ALL")
        self.prompt_file = Path("/data1/wzy/LTXV_ALL/test_val/test_0730.csv")
        self.ltxv_inference_script = Path("/data1/wzy/LTXV_ALL/ltxv_inference.py")
        
        # 统计信息
        self.stats = {
            'total_missing': 0,
            'processed': 0,
            'success': 0,
            'failed': 0
        }
    
    def load_missing_videos(self) -> List[str]:
        """加载缺失视频列表"""
        if not self.missing_videos_file.exists():
            logger.error(f"缺失视频列表文件不存在: {self.missing_videos_file}")
            return []
        
        missing_videos = []
        with open(self.missing_videos_file, 'r', encoding='utf-8') as f:
            for line in f:
                video_name = line.strip()
                if video_name:
                    missing_videos.append(video_name)
        
        logger.info(f"加载了 {len(missing_videos)} 个缺失视频")
        return missing_videos
    
    def load_prompts(self) -> Dict[str, str]:
        """加载提示词"""
        try:
            df = pd.read_csv(self.prompt_file)
            prompts = dict(zip(df['image_id'], df['base_prompt']))
            logger.info(f"加载了 {len(prompts)} 个提示词")
            return prompts
        except Exception as e:
            logger.error(f"加载提示词失败: {e}")
            return {}
    
    def extract_first_frame(self, video_path: Path) -> Path:
        """提取视频首帧"""
        try:
            # 创建临时文件
            temp_dir = Path(tempfile.mkdtemp())
            first_frame_path = temp_dir / f"{video_path.stem}_first_frame.jpg"

            # 使用OpenCV提取首帧
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()

            if ret:
                cv2.imwrite(str(first_frame_path), frame)
                logger.debug(f"首帧提取成功: {first_frame_path}")
                return first_frame_path
            else:
                raise ValueError("无法读取视频首帧")

        except Exception as e:
            logger.error(f"提取首帧失败 {video_path}: {e}")
            raise

    def run_single_inference(self, video_name: str, prompt: str) -> bool:
        """运行单个视频的推理"""
        temp_dir = None
        try:
            # 输入和输出路径
            input_video = self.test_val_dir / video_name
            output_name = video_name.replace(".mp4", "_generated_fixed.mp4")
            output_video = self.all_dir / output_name

            # 检查输入文件
            if not input_video.exists():
                logger.error(f"输入视频不存在: {input_video}")
                return False

            logger.info(f"开始推理: {video_name}")

            # 提取首帧
            first_frame_path = self.extract_first_frame(input_video)
            temp_dir = first_frame_path.parent

            # 导入推理模块
            sys.path.append("/data1/wzy/LTXV_ALL")
            from ltx_video.inference import infer

            # 设置环境变量
            os.environ['HF_HUB_OFFLINE'] = '1'
            os.environ['TRANSFORMERS_OFFLINE'] = '1'

            # 执行推理
            result = infer(
                model_path="/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video",
                conditioning_media_paths=[str(first_frame_path)],
                conditioning_start_frames=[0],
                prompt=prompt,
                negative_prompt="worst quality, inconsistent motion, blurry, jittery, distorted",
                width=480,
                height=854,
                num_frames=121,
                num_inference_steps=40,
                guidance_scale=3.0,
                seed=42,
                output_path=str(output_video),
                device="cuda",
                enable_cpu_offload=True,
                enable_vae_tiling=True,
                torch_dtype="bfloat16"
            )

            # 检查输出文件
            if output_video.exists() and output_video.stat().st_size > 0:
                file_size_mb = output_video.stat().st_size / 1024 / 1024
                logger.info(f"✅ 推理成功: {video_name} -> {output_name} ({file_size_mb:.1f} MB)")
                return True
            else:
                logger.error(f"❌ 推理失败，输出文件无效: {video_name}")
                return False

        except Exception as e:
            logger.error(f"❌ 推理过程出错 {video_name}: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
        finally:
            # 清理临时文件
            if temp_dir and temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)
    
    def run_supplement(self):
        """执行补充推理"""
        start_time = datetime.now()
        
        logger.info("=" * 80)
        logger.info("开始补充推理任务")
        logger.info("=" * 80)
        
        try:
            # 1. 加载缺失视频列表
            missing_videos = self.load_missing_videos()
            if not missing_videos:
                logger.info("未发现缺失视频，任务结束")
                return
            
            self.stats['total_missing'] = len(missing_videos)
            
            # 2. 加载提示词
            prompts = self.load_prompts()
            if not prompts:
                logger.error("无法加载提示词，任务终止")
                return
            
            # 3. 确保输出目录存在
            self.all_dir.mkdir(parents=True, exist_ok=True)
            
            # 4. 检查ltxv_inference.py脚本
            if not self.ltxv_inference_script.exists():
                logger.error(f"推理脚本不存在: {self.ltxv_inference_script}")
                return
            
            # 5. 统计当前文件数量
            current_count = len(list(self.all_dir.glob("*.mp4")))
            logger.info(f"ALL目录当前包含: {current_count} 个视频文件")
            
            # 6. 处理每个缺失的视频
            logger.info(f"开始处理 {len(missing_videos)} 个缺失视频...")
            
            for i, video_name in enumerate(missing_videos, 1):
                logger.info(f"进度: {i}/{len(missing_videos)} - {video_name}")
                
                # 获取提示词
                if video_name not in prompts:
                    logger.error(f"未找到提示词: {video_name}")
                    self.stats['failed'] += 1
                    continue
                
                prompt = prompts[video_name]
                
                # 执行推理
                if self.run_single_inference(video_name, prompt):
                    self.stats['success'] += 1
                else:
                    self.stats['failed'] += 1
                
                self.stats['processed'] += 1
                
                # 显示进度
                success_rate = self.stats['success'] / self.stats['processed'] * 100
                logger.info(f"当前成功率: {success_rate:.1f}% ({self.stats['success']}/{self.stats['processed']})")
            
            # 7. 生成最终报告
            self.generate_final_report(start_time)
            
        except Exception as e:
            logger.error(f"补充推理过程中发生错误: {e}")
            raise
    
    def generate_final_report(self, start_time: datetime):
        """生成最终报告"""
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 重新统计最终文件数量
        final_count = len(list(self.all_dir.glob("*.mp4")))
        
        logger.info("=" * 80)
        logger.info("补充推理完成 - 最终报告")
        logger.info("=" * 80)
        logger.info(f"处理时间: {duration}")
        logger.info(f"缺失视频数: {self.stats['total_missing']} 个")
        logger.info(f"处理总数: {self.stats['processed']} 个")
        logger.info(f"成功处理: {self.stats['success']} 个")
        logger.info(f"处理失败: {self.stats['failed']} 个")
        logger.info(f"最终总数: {final_count} 个视频文件")
        
        # 检查是否达到目标
        if final_count >= 300:
            logger.info("🎉 任务完成！已达到预期的300个视频文件")
        else:
            missing = 300 - final_count
            logger.warning(f"⚠️  仍缺少 {missing} 个视频文件")
        
        success_rate = (self.stats['success'] / self.stats['processed'] * 100) if self.stats['processed'] > 0 else 0
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info("=" * 80)

def main():
    """主函数"""
    supplement = SimpleSupplementInference()
    
    try:
        supplement.run_supplement()
        logger.info("补充推理任务完成！")
    except KeyboardInterrupt:
        logger.info("任务被用户中断")
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
