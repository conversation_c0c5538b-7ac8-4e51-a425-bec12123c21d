#!/usr/bin/env python3
"""
分析缺失视频文件的脚本
用于识别Test_result_multi_gpu_2/gpu_7目录中推理失败的视频文件
"""

import os
import pandas as pd
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def analyze_missing_videos():
    """分析缺失的视频文件"""
    
    # 路径设置
    gpu_7_dir = Path("/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/gpu_7")
    all_dir = Path("/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/ALL")
    test_val_dir = Path("/data1/wzy/LTXV_ALL/test_val")
    prompt_file = Path("/data1/wzy/LTXV_ALL/test_val/test_0730.csv")
    
    logger.info("=" * 80)
    logger.info("开始分析缺失视频文件")
    logger.info("=" * 80)
    
    # 1. 统计ALL目录中当前的视频文件数量
    if all_dir.exists():
        current_videos = list(all_dir.glob("*.mp4"))
        current_count = len(current_videos)
        logger.info(f"ALL目录当前包含: {current_count} 个视频文件")
    else:
        current_count = 0
        logger.info("ALL目录不存在")
    
    # 2. 统计test_val目录中的总视频数量
    if test_val_dir.exists():
        total_videos = list(test_val_dir.glob("*.mp4"))
        total_count = len(total_videos)
        logger.info(f"test_val目录总共有: {total_count} 个视频文件")
    else:
        logger.error("test_val目录不存在")
        return
    
    # 3. 分析gpu_7目录
    if not gpu_7_dir.exists():
        logger.error("gpu_7目录不存在")
        return
    
    # 获取gpu_7目录中的所有子文件夹
    gpu7_folders = [d for d in gpu_7_dir.iterdir() if d.is_dir()]
    logger.info(f"gpu_7目录中找到: {len(gpu7_folders)} 个子文件夹")
    
    # 检查哪些文件夹缺少视频文件
    missing_files = []
    successful_files = []
    
    for folder in gpu7_folders:
        video_file = folder / "video_output_0_the-video-is_0_480x854x121_0.mp4"
        # 正确提取原始文件名：去掉_generated_fixed.mp4后缀
        original_name = folder.name.replace("_generated_fixed.mp4", "")

        if video_file.exists():
            successful_files.append(original_name)
        else:
            missing_files.append(original_name)
    
    logger.info(f"gpu_7中成功的推理: {len(successful_files)} 个")
    logger.info(f"gpu_7中失败的推理: {len(missing_files)} 个")
    
    # 4. 加载提示词文件
    if prompt_file.exists():
        try:
            df = pd.read_csv(prompt_file)
            prompts = dict(zip(df['image_id'], df['base_prompt']))
            logger.info(f"提示词文件包含: {len(prompts)} 个条目")
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            prompts = {}
    else:
        logger.error("提示词文件不存在")
        prompts = {}
    
    # 5. 详细分析缺失文件
    logger.info("\n" + "=" * 60)
    logger.info("缺失文件详细分析")
    logger.info("=" * 60)
    
    valid_missing = []
    for video_name in missing_files:
        # 检查原始视频是否存在
        original_video = test_val_dir / video_name
        has_original = original_video.exists()
        
        # 检查是否有提示词
        has_prompt = video_name in prompts
        
        status = "✅" if (has_original and has_prompt) else "❌"
        logger.info(f"{status} {video_name} - 原始视频: {has_original}, 提示词: {has_prompt}")
        
        if has_original and has_prompt:
            valid_missing.append(video_name)
    
    logger.info(f"\n可以补充推理的视频: {len(valid_missing)} 个")
    
    # 6. 生成补充推理列表
    if valid_missing:
        logger.info("\n需要补充推理的视频列表:")
        for i, video_name in enumerate(valid_missing, 1):
            prompt = prompts.get(video_name, "")[:100] + "..." if len(prompts.get(video_name, "")) > 100 else prompts.get(video_name, "")
            logger.info(f"{i:2d}. {video_name}")
            logger.info(f"    提示词: {prompt}")
    
    # 7. 预测最终结果
    expected_final_count = current_count + len(valid_missing)
    logger.info("\n" + "=" * 60)
    logger.info("预测结果")
    logger.info("=" * 60)
    logger.info(f"当前ALL目录视频数: {current_count}")
    logger.info(f"可补充的视频数: {len(valid_missing)}")
    logger.info(f"预期最终视频数: {expected_final_count}")
    logger.info(f"目标视频数: {total_count}")
    
    if expected_final_count >= total_count:
        logger.info("🎉 补充推理后将达到目标数量！")
    else:
        shortage = total_count - expected_final_count
        logger.warning(f"⚠️  仍将缺少 {shortage} 个视频文件")
    
    return valid_missing, prompts

def main():
    """主函数"""
    try:
        missing_videos, prompts = analyze_missing_videos()
        logger.info("\n分析完成！")
        
        if missing_videos:
            logger.info(f"发现 {len(missing_videos)} 个需要补充推理的视频")
            
            # 保存缺失视频列表到文件
            missing_file = Path("missing_videos.txt")
            with open(missing_file, 'w', encoding='utf-8') as f:
                for video in missing_videos:
                    f.write(f"{video}\n")
            logger.info(f"缺失视频列表已保存到: {missing_file}")
        else:
            logger.info("未发现需要补充推理的视频")
            
    except Exception as e:
        logger.error(f"分析过程中发生错误: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
