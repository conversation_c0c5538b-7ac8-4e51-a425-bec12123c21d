#!/usr/bin/env python3
"""
视频文件整理脚本
用于将Test_result_multi_gpu_2目录下各GPU子文件夹中的视频文件汇集到ALL目录中

功能特性：
- 自动扫描所有GPU子文件夹中的视频文件
- 支持复制或移动操作
- 智能处理文件名冲突（添加数字后缀）
- 实时进度显示和详细统计报告
- 完善的错误处理和日志记录
"""

import os
import shutil
import argparse
from pathlib import Path
from typing import List, Tuple, Dict
import logging
from datetime import datetime

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('video_organize.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class VideoOrganizer:
    """视频文件整理器"""
    
    def __init__(self, source_dir: str, target_dir: str, operation: str = 'copy'):
        """
        初始化视频整理器
        
        Args:
            source_dir: 源目录路径
            target_dir: 目标目录路径
            operation: 操作类型 ('copy' 或 'move')
        """
        self.source_dir = Path(source_dir)
        self.target_dir = Path(target_dir)
        self.operation = operation
        self.video_extensions = {'.mp4', '.avi', '.mov', '.mkv', '.wmv', '.flv'}
        
        # 统计信息
        self.stats = {
            'total_found': 0,
            'total_processed': 0,
            'total_failed': 0,
            'conflicts_resolved': 0,
            'gpu_folders': {}
        }
        
    def find_video_files(self) -> List[Tuple[Path, str]]:
        """
        查找所有GPU子文件夹中的视频文件
        
        Returns:
            List[Tuple[Path, str]]: (文件路径, GPU文件夹名) 的列表
        """
        video_files = []
        
        logger.info(f"开始扫描源目录: {self.source_dir}")
        
        if not self.source_dir.exists():
            logger.error(f"源目录不存在: {self.source_dir}")
            return video_files
            
        # 查找所有GPU子文件夹
        gpu_folders = [d for d in self.source_dir.iterdir() 
                      if d.is_dir() and d.name.startswith('gpu_')]
        
        if not gpu_folders:
            logger.warning("未找到任何GPU子文件夹")
            return video_files
            
        logger.info(f"找到 {len(gpu_folders)} 个GPU文件夹")
        
        # 扫描每个GPU文件夹
        for gpu_folder in sorted(gpu_folders):
            gpu_name = gpu_folder.name
            folder_videos = []
            
            logger.info(f"扫描 {gpu_name} 文件夹...")
            
            # 递归查找视频文件
            for file_path in gpu_folder.rglob('*'):
                if file_path.is_file() and file_path.suffix.lower() in self.video_extensions:
                    folder_videos.append((file_path, gpu_name))
                    video_files.append((file_path, gpu_name))
            
            self.stats['gpu_folders'][gpu_name] = len(folder_videos)
            logger.info(f"  - 找到 {len(folder_videos)} 个视频文件")
        
        self.stats['total_found'] = len(video_files)
        logger.info(f"总共找到 {len(video_files)} 个视频文件")
        
        return video_files
    
    def resolve_filename_conflict(self, target_path: Path) -> Path:
        """
        解决文件名冲突
        
        Args:
            target_path: 目标文件路径
            
        Returns:
            Path: 解决冲突后的文件路径
        """
        if not target_path.exists():
            return target_path
            
        # 生成新的文件名（添加数字后缀）
        base_name = target_path.stem
        extension = target_path.suffix
        parent_dir = target_path.parent
        
        counter = 1
        while True:
            new_name = f"{base_name}_{counter}{extension}"
            new_path = parent_dir / new_name
            if not new_path.exists():
                self.stats['conflicts_resolved'] += 1
                logger.info(f"文件名冲突已解决: {target_path.name} -> {new_name}")
                return new_path
            counter += 1
    
    def extract_meaningful_name(self, source_path: Path) -> str:
        """
        从文件路径中提取有意义的文件名

        Args:
            source_path: 源文件路径

        Returns:
            str: 提取的有意义文件名
        """
        # 如果文件名是通用的video_output格式，使用父目录名
        if source_path.name.startswith('video_output_'):
            parent_name = source_path.parent.name
            # 如果父目录名以.mp4结尾，直接使用；否则添加.mp4扩展名
            if parent_name.endswith('.mp4'):
                return parent_name
            else:
                return f"{parent_name}.mp4"
        else:
            # 否则使用原始文件名
            return source_path.name

    def process_file(self, source_path: Path, gpu_name: str) -> bool:
        """
        处理单个视频文件

        Args:
            source_path: 源文件路径
            gpu_name: GPU文件夹名

        Returns:
            bool: 处理是否成功
        """
        try:
            # 提取有意义的文件名
            meaningful_name = self.extract_meaningful_name(source_path)

            # 确定目标文件路径
            target_path = self.target_dir / meaningful_name

            # 解决文件名冲突
            target_path = self.resolve_filename_conflict(target_path)

            # 执行文件操作
            if self.operation == 'copy':
                shutil.copy2(source_path, target_path)
                logger.debug(f"复制: {source_path} -> {target_path}")
            elif self.operation == 'move':
                shutil.move(str(source_path), str(target_path))
                logger.debug(f"移动: {source_path} -> {target_path}")
            else:
                raise ValueError(f"不支持的操作类型: {self.operation}")

            return True

        except Exception as e:
            logger.error(f"处理文件失败 {source_path}: {e}")
            return False
    
    def create_target_directory(self):
        """创建目标目录"""
        try:
            self.target_dir.mkdir(parents=True, exist_ok=True)
            logger.info(f"目标目录已准备: {self.target_dir}")
        except Exception as e:
            logger.error(f"创建目标目录失败: {e}")
            raise
    
    def organize_videos(self):
        """执行视频文件整理"""
        start_time = datetime.now()
        logger.info("=" * 60)
        logger.info("开始视频文件整理任务")
        logger.info(f"源目录: {self.source_dir}")
        logger.info(f"目标目录: {self.target_dir}")
        logger.info(f"操作类型: {self.operation}")
        logger.info("=" * 60)
        
        try:
            # 创建目标目录
            self.create_target_directory()
            
            # 查找所有视频文件
            video_files = self.find_video_files()
            
            if not video_files:
                logger.warning("未找到任何视频文件，任务结束")
                return
            
            # 处理每个视频文件
            logger.info(f"开始处理 {len(video_files)} 个视频文件...")
            
            for i, (source_path, gpu_name) in enumerate(video_files, 1):
                print(f"\r进度: {i}/{len(video_files)} ({i/len(video_files)*100:.1f}%)", end='', flush=True)
                
                if self.process_file(source_path, gpu_name):
                    self.stats['total_processed'] += 1
                else:
                    self.stats['total_failed'] += 1
            
            print()  # 换行
            
        except Exception as e:
            logger.error(f"整理过程中发生错误: {e}")
            raise
        finally:
            # 生成统计报告
            self.generate_report(start_time)
    
    def generate_report(self, start_time: datetime):
        """生成统计报告"""
        end_time = datetime.now()
        duration = end_time - start_time
        
        logger.info("=" * 60)
        logger.info("视频文件整理完成 - 统计报告")
        logger.info("=" * 60)
        logger.info(f"处理时间: {duration}")
        logger.info(f"总共找到: {self.stats['total_found']} 个视频文件")
        logger.info(f"成功处理: {self.stats['total_processed']} 个文件")
        logger.info(f"处理失败: {self.stats['total_failed']} 个文件")
        logger.info(f"冲突解决: {self.stats['conflicts_resolved']} 个文件")
        
        logger.info("\n各GPU文件夹统计:")
        for gpu_name, count in sorted(self.stats['gpu_folders'].items()):
            logger.info(f"  {gpu_name}: {count} 个文件")
        
        success_rate = (self.stats['total_processed'] / self.stats['total_found'] * 100) if self.stats['total_found'] > 0 else 0
        logger.info(f"\n成功率: {success_rate:.1f}%")
        logger.info("=" * 60)

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='视频文件整理脚本')
    parser.add_argument('--source', '-s', 
                       default='/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2',
                       help='源目录路径 (默认: /data1/wzy/LTXV_ALL/Test_result_multi_gpu_2)')
    parser.add_argument('--target', '-t',
                       default='/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/ALL',
                       help='目标目录路径 (默认: /data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/ALL)')
    parser.add_argument('--operation', '-o',
                       choices=['copy', 'move'],
                       default='copy',
                       help='操作类型: copy(复制) 或 move(移动) (默认: copy)')
    parser.add_argument('--verbose', '-v',
                       action='store_true',
                       help='显示详细日志')
    
    args = parser.parse_args()
    
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    
    # 创建视频整理器并执行任务
    organizer = VideoOrganizer(args.source, args.target, args.operation)
    
    try:
        organizer.organize_videos()
        logger.info("任务完成！")
    except KeyboardInterrupt:
        logger.info("任务被用户中断")
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
