#!/usr/bin/env python3
"""
补充推理脚本
用于处理Test_result_multi_gpu_2/gpu_7目录中推理失败的视频文件

功能：
- 识别推理失败的视频任务
- 基于首帧+文本提示词进行推理
- 直接输出到ALL目录
- 确保最终达到300个视频文件
"""

import os
import sys
import csv
import cv2
import torch
import tempfile
import logging
import numpy as np
from pathlib import Path
from typing import List, Dict, Tuple
from datetime import datetime
import pandas as pd

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('supplement_inference.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class SupplementInference:
    """补充推理器"""
    
    def __init__(self):
        """初始化补充推理器"""
        self.gpu_7_dir = Path("/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/gpu_7")
        self.all_dir = Path("/data1/wzy/LTXV_ALL/Test_result_multi_gpu_2/ALL")
        self.test_val_dir = Path("/data1/wzy/LTXV_ALL/test_val")
        self.prompt_file = Path("/data1/wzy/LTXV_ALL/test_val/test_0730.csv")
        self.model_path = "/data1/wzy/LTXV_ALL/LTXV_models/LTX-Video"
        
        # 统计信息
        self.stats = {
            'total_expected': 300,
            'current_count': 0,
            'missing_count': 0,
            'failed_tasks': [],
            'processed_count': 0,
            'success_count': 0,
            'failed_count': 0
        }
        
        # 初始化模型相关变量
        self.pipeline = None
        self.device = None
        
    def analyze_missing_files(self) -> List[str]:
        """分析缺失的视频文件"""
        logger.info("开始分析缺失的视频文件...")
        
        # 获取gpu_7目录中的所有子文件夹
        gpu7_folders = [d.name for d in self.gpu_7_dir.iterdir() if d.is_dir()]
        logger.info(f"gpu_7目录中找到 {len(gpu7_folders)} 个子文件夹")
        
        # 检查哪些文件夹缺少视频文件
        missing_files = []
        for folder_name in gpu7_folders:
            video_file = self.gpu_7_dir / folder_name / "video_output_0_the-video-is_0_480x854x121_0.mp4"
            if not video_file.exists():
                # 提取原始视频名（去掉_generated_fixed.mp4后缀）
                original_name = folder_name.replace("_generated_fixed.mp4", ".mp4")
                missing_files.append(original_name)
                logger.info(f"缺失视频: {original_name}")
        
        self.stats['failed_tasks'] = missing_files
        self.stats['missing_count'] = len(missing_files)
        
        logger.info(f"发现 {len(missing_files)} 个缺失的视频文件")
        return missing_files
    
    def load_prompts(self) -> Dict[str, str]:
        """加载提示词文件"""
        logger.info(f"加载提示词文件: {self.prompt_file}")
        
        prompts = {}
        try:
            df = pd.read_csv(self.prompt_file)
            for _, row in df.iterrows():
                prompts[row['image_id']] = row['base_prompt']
            
            logger.info(f"成功加载 {len(prompts)} 个提示词")
            return prompts
            
        except Exception as e:
            logger.error(f"加载提示词文件失败: {e}")
            return {}
    
    def extract_first_frame(self, video_path: Path) -> Path:
        """提取视频首帧"""
        try:
            # 创建临时文件
            temp_dir = Path(tempfile.mkdtemp())
            first_frame_path = temp_dir / f"{video_path.stem}_first_frame.jpg"
            
            # 使用OpenCV提取首帧
            cap = cv2.VideoCapture(str(video_path))
            ret, frame = cap.read()
            cap.release()
            
            if ret:
                cv2.imwrite(str(first_frame_path), frame)
                logger.debug(f"首帧提取成功: {first_frame_path}")
                return first_frame_path
            else:
                raise ValueError("无法读取视频首帧")
                
        except Exception as e:
            logger.error(f"提取首帧失败 {video_path}: {e}")
            raise
    
    def initialize_model(self):
        """初始化LTX-Video模型"""
        logger.info("初始化LTX-Video模型...")

        try:
            # 检查GPU可用性
            if torch.cuda.is_available():
                self.device = "cuda"
                logger.info(f"使用GPU: {torch.cuda.get_device_name()}")
            else:
                self.device = "cpu"
                logger.warning("GPU不可用，使用CPU")

            # 添加LTX-Video路径
            sys.path.append("/data1/wzy/LTXV_ALL/LTX-Video")

            # 设置环境变量
            os.environ['HF_HUB_OFFLINE'] = '1'
            os.environ['TRANSFORMERS_OFFLINE'] = '1'

            logger.info("模型初始化完成")
            return True

        except Exception as e:
            logger.error(f"模型初始化失败: {e}")
            return False
    
    def run_inference(self, video_path: Path, prompt: str, output_path: Path) -> bool:
        """运行单个视频推理"""
        temp_dir = None
        try:
            logger.info(f"开始推理: {video_path.name}")

            # 提取首帧
            first_frame_path = self.extract_first_frame(video_path)
            temp_dir = first_frame_path.parent

            # 使用简化的推理方法
            success = self._run_ltx_inference(first_frame_path, prompt, output_path)

            return success

        except Exception as e:
            logger.error(f"推理过程出错 {video_path.name}: {e}")
            return False
        finally:
            # 清理临时文件
            if temp_dir and temp_dir.exists():
                import shutil
                shutil.rmtree(temp_dir, ignore_errors=True)

    def _run_ltx_inference(self, first_frame_path: Path, prompt: str, output_path: Path) -> bool:
        """运行LTX-Video推理的核心逻辑"""
        try:
            # 导入LTX-Video推理模块
            from ltx_video.inference import infer, InferenceConfig

            logger.info("开始LTX-Video推理...")

            # 创建推理配置
            config = InferenceConfig(
                model_path=self.model_path,
                conditioning_media_paths=[str(first_frame_path)],
                conditioning_start_frames=[0],
                prompt=prompt,
                negative_prompt="worst quality, inconsistent motion, blurry, jittery, distorted",
                width=480,
                height=854,
                num_frames=121,
                num_inference_steps=40,
                guidance_scale=3.0,
                seed=42,
                output_path=str(output_path),
                device=self.device,
                enable_cpu_offload=True,
                enable_vae_tiling=True,
                torch_dtype="bfloat16"
            )

            # 执行推理
            logger.info("执行视频生成...")
            result = infer(config)

            # 验证输出文件
            if output_path.exists() and output_path.stat().st_size > 0:
                file_size_mb = output_path.stat().st_size / 1024 / 1024
                logger.info(f"✅ 推理成功: {output_path} ({file_size_mb:.1f} MB)")
                return True
            else:
                logger.error(f"❌ 推理失败，输出文件无效: {output_path}")
                return False

        except Exception as e:
            logger.error(f"LTX推理过程出错: {e}")
            import traceback
            logger.error(traceback.format_exc())
            return False
    
    def count_current_files(self) -> int:
        """统计ALL目录中当前的视频文件数量"""
        if not self.all_dir.exists():
            return 0
        
        count = len(list(self.all_dir.glob("*.mp4")))
        self.stats['current_count'] = count
        return count
    
    def run_supplement(self):
        """执行补充推理"""
        start_time = datetime.now()
        
        logger.info("=" * 80)
        logger.info("开始补充推理任务")
        logger.info("=" * 80)
        
        try:
            # 1. 统计当前文件数量
            current_count = self.count_current_files()
            logger.info(f"ALL目录当前包含 {current_count} 个视频文件")
            
            # 2. 分析缺失文件
            missing_files = self.analyze_missing_files()
            if not missing_files:
                logger.info("未发现缺失文件，任务完成")
                return
            
            # 3. 加载提示词
            prompts = self.load_prompts()
            if not prompts:
                logger.error("无法加载提示词，任务终止")
                return
            
            # 4. 初始化模型
            if not self.initialize_model():
                logger.error("模型初始化失败，任务终止")
                return
            
            # 5. 确保输出目录存在
            self.all_dir.mkdir(parents=True, exist_ok=True)
            
            # 6. 处理每个缺失的视频
            logger.info(f"开始处理 {len(missing_files)} 个缺失视频...")
            
            for i, video_name in enumerate(missing_files, 1):
                logger.info(f"处理进度: {i}/{len(missing_files)} - {video_name}")
                
                # 检查原始视频文件是否存在
                video_path = self.test_val_dir / video_name
                if not video_path.exists():
                    logger.error(f"原始视频文件不存在: {video_path}")
                    self.stats['failed_count'] += 1
                    continue
                
                # 获取对应的提示词
                if video_name not in prompts:
                    logger.error(f"未找到提示词: {video_name}")
                    self.stats['failed_count'] += 1
                    continue
                
                prompt = prompts[video_name]
                
                # 设置输出路径
                output_name = video_name.replace(".mp4", "_generated_fixed.mp4")
                output_path = self.all_dir / output_name
                
                # 执行推理
                if self.run_inference(video_path, prompt, output_path):
                    self.stats['success_count'] += 1
                    logger.info(f"✅ 成功处理: {video_name}")
                else:
                    self.stats['failed_count'] += 1
                    logger.error(f"❌ 处理失败: {video_name}")
                
                self.stats['processed_count'] += 1
            
            # 7. 生成最终报告
            self.generate_final_report(start_time)
            
        except Exception as e:
            logger.error(f"补充推理过程中发生错误: {e}")
            raise
    
    def generate_final_report(self, start_time: datetime):
        """生成最终报告"""
        end_time = datetime.now()
        duration = end_time - start_time
        
        # 重新统计最终文件数量
        final_count = self.count_current_files()
        
        logger.info("=" * 80)
        logger.info("补充推理完成 - 最终报告")
        logger.info("=" * 80)
        logger.info(f"处理时间: {duration}")
        logger.info(f"预期总数: {self.stats['total_expected']} 个视频")
        logger.info(f"处理前数量: {self.stats['current_count']} 个视频")
        logger.info(f"发现缺失: {self.stats['missing_count']} 个视频")
        logger.info(f"处理总数: {self.stats['processed_count']} 个视频")
        logger.info(f"成功处理: {self.stats['success_count']} 个视频")
        logger.info(f"处理失败: {self.stats['failed_count']} 个视频")
        logger.info(f"最终总数: {final_count} 个视频")
        
        # 检查是否达到目标
        if final_count >= self.stats['total_expected']:
            logger.info("🎉 任务完成！已达到预期的300个视频文件")
        else:
            missing = self.stats['total_expected'] - final_count
            logger.warning(f"⚠️  仍缺少 {missing} 个视频文件")
        
        success_rate = (self.stats['success_count'] / self.stats['processed_count'] * 100) if self.stats['processed_count'] > 0 else 0
        logger.info(f"成功率: {success_rate:.1f}%")
        logger.info("=" * 80)

def main():
    """主函数"""
    supplement = SupplementInference()
    
    try:
        supplement.run_supplement()
        logger.info("补充推理任务完成！")
    except KeyboardInterrupt:
        logger.info("任务被用户中断")
    except Exception as e:
        logger.error(f"任务执行失败: {e}")
        return 1
    
    return 0

if __name__ == '__main__':
    exit(main())
