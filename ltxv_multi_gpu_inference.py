#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
LTX-Video 多GPU并行推理脚本
基于修改后的ltxv_inference.py实现多进程并行推理

使用方法:
python ltxv_multi_gpu_inference.py --video_dir /data1/wzy/LTXV_ALL/test_val --prompts_csv /data1/wzy/LTXV_ALL/test_val/test_0730.csv
"""

import os
import sys
import csv
import json
import time
import logging
import argparse
import subprocess
import multiprocessing as mp
import threading
from pathlib import Path
from datetime import datetime
from typing import List, Dict, Tuple

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler('ltxv_multi_gpu_inference.log')
    ]
)
logger = logging.getLogger('LTXV-MultiGPU')


def get_gpu_memory_usage():
    """获取所有GPU的内存使用情况"""
    try:
        result = subprocess.run(['nvidia-smi', '--query-gpu=index,memory.used,memory.total,memory.free', 
                               '--format=csv,noheader,nounits'], 
                               capture_output=True, text=True, check=True)
        
        gpu_info = []
        for line in result.stdout.strip().split('\n'):
            if line.strip():
                parts = line.split(', ')
                gpu_id = int(parts[0])
                memory_used = int(parts[1])
                memory_total = int(parts[2])
                memory_free = int(parts[3])
                
                gpu_info.append({
                    'gpu_id': gpu_id,
                    'memory_used': memory_used,
                    'memory_total': memory_total,
                    'memory_free': memory_free,
                    'usage_percent': (memory_used / memory_total) * 100
                })
        
        return gpu_info
    except Exception as e:
        logger.error(f"获取GPU信息失败: {e}")
        return []


def get_available_gpus(min_free_memory_mb=10000, max_usage_percent=20):
    """获取可用的GPU列表"""
    gpu_info = get_gpu_memory_usage()
    available_gpus = []
    
    for gpu in gpu_info:
        if (gpu['memory_free'] >= min_free_memory_mb and 
            gpu['usage_percent'] <= max_usage_percent):
            available_gpus.append(gpu['gpu_id'])
            logger.info(f"GPU {gpu['gpu_id']}: 可用 - "
                       f"空闲内存: {gpu['memory_free']}MB, "
                       f"使用率: {gpu['usage_percent']:.1f}%")
        else:
            logger.warning(f"GPU {gpu['gpu_id']}: 不可用 - "
                          f"空闲内存: {gpu['memory_free']}MB, "
                          f"使用率: {gpu['usage_percent']:.1f}%")
    
    return available_gpus


def load_prompts_from_csv(csv_path):
    """从CSV文件中加载提示词"""
    if not os.path.exists(csv_path):
        raise FileNotFoundError(f"CSV文件不存在: {csv_path}")

    prompts_dict = {}
    try:
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
        for encoding in encodings:
            try:
                with open(csv_path, 'r', encoding=encoding) as f:
                    reader = csv.DictReader(f)
                    for row in reader:
                        if 'image_id' in row and 'base_prompt' in row:
                            prompts_dict[row['image_id']] = row['base_prompt']
                logger.info(f"使用编码 {encoding} 从 {csv_path} 加载了 {len(prompts_dict)} 个提示词")
                return prompts_dict
            except UnicodeDecodeError:
                continue
        
        raise ValueError(f"无法使用任何编码读取CSV文件: {csv_path}")
    except Exception as e:
        logger.error(f"加载CSV文件时出错: {e}")
        raise


def extract_image_id(video_path):
    """从视频文件路径中提取image_id"""
    filename = os.path.basename(video_path)
    # CSV文件中的image_id是完整的文件名，所以直接返回文件名
    return filename


def distribute_tasks(video_files, prompts_dict, num_gpus):
    """将任务均匀分配给各个GPU"""
    # 过滤出有提示词的视频文件
    valid_tasks = []
    for video_file in video_files:
        image_id = extract_image_id(video_file)
        if image_id in prompts_dict:
            valid_tasks.append({
                'video_file': video_file,
                'image_id': image_id,
                'prompt': prompts_dict[image_id]
            })
    
    logger.info(f"总共有 {len(valid_tasks)} 个有效任务")
    
    # 均匀分配任务
    tasks_per_gpu = []
    for i in range(num_gpus):
        tasks_per_gpu.append([])
    
    for i, task in enumerate(valid_tasks):
        gpu_index = i % num_gpus
        tasks_per_gpu[gpu_index].append(task)
    
    # 显示分配结果
    for i, tasks in enumerate(tasks_per_gpu):
        logger.info(f"GPU {i}: 分配了 {len(tasks)} 个任务")
    
    return tasks_per_gpu


def monitor_progress(output_dir, available_gpus, total_tasks, interval=30):
    """监控所有GPU的推理进度"""
    while True:
        time.sleep(interval)

        total_completed = 0
        for gpu_id in available_gpus:
            gpu_output_dir = os.path.join(output_dir, f"gpu_{gpu_id}")
            if os.path.exists(gpu_output_dir):
                completed_files = [f for f in os.listdir(gpu_output_dir) if f.endswith('.mp4')]
                total_completed += len(completed_files)

        progress_percent = (total_completed / total_tasks) * 100 if total_tasks > 0 else 0
        logger.info(f"总体进度: {total_completed}/{total_tasks} ({progress_percent:.1f}%)")

        if total_completed >= total_tasks:
            break


def run_single_gpu_inference(gpu_id, tasks, args, process_id):
    """在单个GPU上运行推理"""
    # 为每个进程创建独立的日志记录器
    process_logger = logging.getLogger(f'LTXV-GPU{gpu_id}')
    process_logger.info(f"进程 {process_id} 在GPU {gpu_id}上开始处理 {len(tasks)} 个任务")

    # 设置CUDA设备
    os.environ['CUDA_VISIBLE_DEVICES'] = str(gpu_id)
    
    # 创建GPU专用输出目录
    gpu_output_dir = os.path.join(args.output_dir, f"gpu_{gpu_id}")
    os.makedirs(gpu_output_dir, exist_ok=True)
    
    successful_count = 0
    failed_count = 0
    
    for i, task in enumerate(tasks, 1):
        try:
            video_file = task['video_file']
            image_id = task['image_id']
            prompt = task['prompt']
            
            video_path = os.path.join(args.video_dir, video_file)
            output_path = os.path.join(gpu_output_dir, f"{image_id}_test.mp4")
            
            # 如果输出文件已存在，跳过
            if os.path.exists(output_path):
                process_logger.info(f"输出文件已存在，跳过: {output_path}")
                successful_count += 1
                continue

            process_logger.info(f"[{i}/{len(tasks)}] 处理: {video_file}")
            process_logger.info(f"提示词: {prompt[:50]}...")
            
            # 构建推理命令
            cmd = [
                sys.executable, 'ltxv_inference.py',
                '--video_dir', args.video_dir,
                '--prompts_csv', args.prompts_csv,
                '--output_dir', gpu_output_dir,
                '--single_video', video_file,
                '--model_path', args.model_path,
                '--config_path', args.config_path,
                '--seed', str(args.seed),
                '--num_frames', str(args.num_frames),
                '--height', str(args.height),
                '--width', str(args.width)
            ]
            
            if args.use_13b_model:
                cmd.append('--use_13b_model')
            
            if args.disable_memory_adjustment:
                cmd.append('--disable_memory_adjustment')
            
            # 执行推理
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                process_logger.info(f"推理成功: {video_file}")
                successful_count += 1
            else:
                process_logger.error(f"推理失败: {video_file}")
                process_logger.error(f"错误输出: {result.stderr}")
                failed_count += 1

        except subprocess.TimeoutExpired:
            process_logger.error(f"推理超时: {video_file}")
            failed_count += 1
        except Exception as e:
            process_logger.error(f"处理任务时出错: {e}")
            failed_count += 1

    process_logger.info(f"GPU {gpu_id} 处理完成! 成功: {successful_count}, 失败: {failed_count}")
    return successful_count, failed_count


def main():
    parser = argparse.ArgumentParser(description='LTX-Video 多GPU并行推理')
    parser.add_argument('--video_dir', type=str, default='/data1/wzy/LTXV_ALL/test_val',
                        help='视频文件夹路径')
    parser.add_argument('--prompts_csv', type=str, default='/data1/wzy/LTXV_ALL/test_val/test_0730.csv',
                        help='提示词CSV文件路径')
    parser.add_argument('--output_dir', type=str, default='/data1/wzy/LTXV_ALL/Test_result_multi_gpu_3',
                        help='输出文件夹路径')
    parser.add_argument('--model_path', type=str, default='LTXV_models/LTX-Video/ltxv-2b-0.9.6-dev-04-25.safetensors',
                        help='模型文件路径')
    parser.add_argument('--config_path', type=str, default='ltxv_2b_fixed_config.yaml',
                        help='配置文件路径')
    parser.add_argument('--seed', type=int, default=0,
                        help='随机种子')
    parser.add_argument('--num_frames', type=int, default=121,
                        help='生成的帧数')
    parser.add_argument('--height', type=int, default=480,
                        help='输出视频高度')
    parser.add_argument('--width', type=int, default=854,
                        help='输出视频宽度')
    parser.add_argument('--use_13b_model', action='store_true',
                        help='使用13B模型而不是2B模型')
    parser.add_argument('--disable_memory_adjustment', action='store_true',
                        help='禁用自动内存调整')
    parser.add_argument('--min_free_memory', type=int, default=10000,
                        help='GPU最小空闲内存要求(MB)')
    parser.add_argument('--max_usage_percent', type=float, default=20,
                        help='GPU最大使用率要求(%)')

    args = parser.parse_args()

    logger.info("开始多GPU并行推理...")
    
    # 检查必要文件
    if not os.path.exists(args.video_dir):
        logger.error(f"视频文件夹不存在: {args.video_dir}")
        return
    
    if not os.path.exists(args.prompts_csv):
        logger.error(f"提示词文件不存在: {args.prompts_csv}")
        return
    
    if not os.path.exists(args.model_path):
        logger.error(f"模型文件不存在: {args.model_path}")
        return
    
    if not os.path.exists(args.config_path):
        logger.error(f"配置文件不存在: {args.config_path}")
        return
    
    # 获取可用GPU
    available_gpus = get_available_gpus(args.min_free_memory, args.max_usage_percent)
    if not available_gpus:
        logger.error("没有可用的GPU")
        return
    
    logger.info(f"找到 {len(available_gpus)} 个可用GPU: {available_gpus}")
    
    # 加载提示词
    prompts_dict = load_prompts_from_csv(args.prompts_csv)
    
    # 获取视频文件列表
    video_files = [f for f in os.listdir(args.video_dir) if f.endswith('.mp4')]
    if not video_files:
        logger.error(f"在文件夹 {args.video_dir} 中未找到任何.mp4视频文件")
        return
    
    logger.info(f"找到 {len(video_files)} 个视频文件")
    
    # 分配任务
    tasks_per_gpu = distribute_tasks(video_files, prompts_dict, len(available_gpus))
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 计算总任务数
    total_tasks = sum(len(tasks) for tasks in tasks_per_gpu)

    # 启动多进程推理
    processes = []
    start_time = time.time()

    # 启动进度监控线程
    monitor_thread = threading.Thread(
        target=monitor_progress,
        args=(args.output_dir, available_gpus, total_tasks, 30),
        daemon=True
    )
    monitor_thread.start()

    for i, (gpu_id, tasks) in enumerate(zip(available_gpus, tasks_per_gpu)):
        if tasks:  # 只为有任务的GPU启动进程
            process = mp.Process(
                target=run_single_gpu_inference,
                args=(gpu_id, tasks, args, i)
            )
            process.start()
            processes.append(process)
            logger.info(f"已启动GPU {gpu_id}的推理进程")

    # 等待所有进程完成
    logger.info("等待所有GPU进程完成...")
    for process in processes:
        process.join()
    
    end_time = time.time()
    total_time = end_time - start_time
    
    logger.info(f"所有GPU推理完成! 总耗时: {total_time:.1f} 秒")
    
    # 统计结果
    total_successful = 0

    for gpu_id in available_gpus:
        gpu_output_dir = os.path.join(args.output_dir, f"gpu_{gpu_id}")
        if os.path.exists(gpu_output_dir):
            output_files = [f for f in os.listdir(gpu_output_dir) if f.endswith('.mp4')]
            total_successful += len(output_files)
            logger.info(f"GPU {gpu_id}: 生成了 {len(output_files)} 个视频文件")

    logger.info(f"多GPU推理统计 - 成功: {total_successful}, 总耗时: {total_time:.1f}秒")


if __name__ == "__main__":
    main()
